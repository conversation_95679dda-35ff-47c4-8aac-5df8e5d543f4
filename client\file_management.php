<?php
session_start();

if (!isset($_SESSION['nama'])) {
    header("Location: ../index.php");
    exit;
}

if ($_SESSION['level'] != "client") {
    echo "<script>alert('Anda tidak memiliki akses ke halaman ini!'); window.location='../index.php';</script>";
    exit;
}

require '../koneksi.php';
require '../file_permissions.php';

// Get user accessible files
$user_id = $_SESSION['id_petugas'] ?? 0;
$user_level = $_SESSION['level'];
$files = getUserAccessibleFiles($koneksi, $user_id, $user_level);

// Filter parameters
$status_filter = $_GET['status'] ?? '';
$type_filter = $_GET['type'] ?? '';
$date_filter = $_GET['date'] ?? '';

// Apply filters
if (!empty($status_filter) || !empty($type_filter) || !empty($date_filter)) {
    $files = array_filter($files, function($file) use ($status_filter, $type_filter, $date_filter) {
        $match = true;
        
        if (!empty($status_filter) && $file['status'] != $status_filter) {
            $match = false;
        }
        
        if (!empty($type_filter)) {
            $file_ext = strtolower(pathinfo($file['gambar'], PATHINFO_EXTENSION));
            if ($file_ext != $type_filter) {
                $match = false;
            }
        }
        
        if (!empty($date_filter)) {
            $file_date = date('Y-m-d', strtotime($file['created_at']));
            if ($file_date != $date_filter) {
                $match = false;
            }
        }
        
        return $match;
    });
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <title>File Management - Client</title>
    
    <!-- Custom fonts for this template-->
    <link href="../tmp/vendor/fontawesome-free/css/all.min.css" rel="stylesheet" type="text/css">
    <link href="https://fonts.googleapis.com/css?family=Nunito:200,200i,300,300i,400,400i,600,600i,700,700i,800,800i,900,900i" rel="stylesheet">
    
    <!-- Custom styles for this template-->
    <link href="../tmp/css/sb-admin-2.min.css" rel="stylesheet">
    
    <style>
        .file-preview {
            max-width: 100px;
            max-height: 100px;
            object-fit: cover;
        }
        .file-icon {
            font-size: 3rem;
            color: #5a5c69;
        }
        .status-badge {
            font-size: 0.75rem;
        }
        .filter-card {
            background: #f8f9fc;
            border: 1px solid #e3e6f0;
            border-radius: 0.35rem;
            padding: 1rem;
            margin-bottom: 1rem;
        }
    </style>
</head>

<body id="page-top">
    <div id="wrapper">
        <!-- Sidebar -->
        <ul class="navbar-nav bg-gradient-primary sidebar sidebar-dark accordion" id="accordionSidebar">
            <a class="sidebar-brand d-flex align-items-center justify-content-center" href="client.php">
                <div class="sidebar-brand-icon rotate-n-15">
                    <i class="fas fa-keyboard"></i>
                </div>
                <div class="sidebar-brand-text mx-3">Client Portal</div>
            </a>

            <hr class="sidebar-divider my-0">

            <li class="nav-item">
                <a class="nav-link" href="client.php">
                    <i class="fas fa-fw fa-tachometer-alt"></i>
                    <span>Dashboard</span>
                </a>
            </li>

            <hr class="sidebar-divider">

            <div class="sidebar-heading">Interface</div>

            <li class="nav-item">
                <a class="nav-link" href="lihat_progress.php">
                    <i class="fas fa-fw fa-chart-line"></i>
                    <span>Lihat Progress</span>
                </a>
            </li>

            <li class="nav-item active">
                <a class="nav-link" href="file_management.php">
                    <i class="fas fa-fw fa-folder"></i>
                    <span>File Management</span>
                </a>
            </li>

            <hr class="sidebar-divider d-none d-md-block">

            <li class="nav-item">
                <a class="nav-link" href="../logout.php">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>Keluar</span>
                </a>
            </li>

            <div class="text-center d-none d-md-inline">
                <button class="rounded-circle border-0" id="sidebarToggle"></button>
            </div>
        </ul>

        <!-- Content Wrapper -->
        <div id="content-wrapper" class="d-flex flex-column">
            <div id="content">
                <!-- Topbar -->
                <nav class="navbar navbar-expand navbar-light bg-white topbar mb-4 static-top shadow">
                    <button id="sidebarToggleTop" class="btn btn-link d-md-none rounded-circle mr-3">
                        <i class="fa fa-bars"></i>
                    </button>
                    <h1 class="h4 mb-0 text-gray-800">File Management</h1>
                </nav>

                <!-- Begin Page Content -->
                <div class="container-fluid">
                    <!-- Filters -->
                    <div class="filter-card">
                        <form method="GET" class="row">
                            <div class="col-md-3">
                                <label for="status">Status:</label>
                                <select name="status" id="status" class="form-control form-control-sm">
                                    <option value="">Semua Status</option>
                                    <option value="pending" <?= $status_filter == 'pending' ? 'selected' : '' ?>>Pending</option>
                                    <option value="approved" <?= $status_filter == 'approved' ? 'selected' : '' ?>>Approved</option>
                                    <option value="rejected" <?= $status_filter == 'rejected' ? 'selected' : '' ?>>Rejected</option>
                                    <option value="revision" <?= $status_filter == 'revision' ? 'selected' : '' ?>>Revision</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="type">Jenis File:</label>
                                <select name="type" id="type" class="form-control form-control-sm">
                                    <option value="">Semua Jenis</option>
                                    <option value="pdf" <?= $type_filter == 'pdf' ? 'selected' : '' ?>>PDF</option>
                                    <option value="jpg" <?= $type_filter == 'jpg' ? 'selected' : '' ?>>JPG</option>
                                    <option value="png" <?= $type_filter == 'png' ? 'selected' : '' ?>>PNG</option>
                                    <option value="dwg" <?= $type_filter == 'dwg' ? 'selected' : '' ?>>DWG</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="date">Tanggal:</label>
                                <input type="date" name="date" id="date" class="form-control form-control-sm" value="<?= htmlspecialchars($date_filter) ?>">
                            </div>
                            <div class="col-md-3 d-flex align-items-end">
                                <button type="submit" class="btn btn-primary btn-sm mr-2">Filter</button>
                                <a href="file_management.php" class="btn btn-secondary btn-sm">Reset</a>
                            </div>
                        </form>
                    </div>

                    <!-- Files Grid -->
                    <div class="row">
                        <?php if (empty($files)): ?>
                            <div class="col-12">
                                <div class="card shadow">
                                    <div class="card-body text-center">
                                        <i class="fas fa-folder-open fa-3x text-gray-300 mb-3"></i>
                                        <h5 class="text-gray-600">Tidak ada file yang tersedia</h5>
                                        <p class="text-gray-500">Belum ada file yang dapat diakses atau sesuai dengan filter yang dipilih.</p>
                                    </div>
                                </div>
                            </div>
                        <?php else: ?>
                            <?php foreach ($files as $file): ?>
                                <?php
                                $file_ext = strtolower(pathinfo($file['gambar'], PATHINFO_EXTENSION));
                                $is_image = in_array($file_ext, ['jpg', 'jpeg', 'png', 'gif']);
                                $file_url = "../file_handler.php?id=" . $file['id'] . "&action=view";
                                $download_url = "../file_handler.php?id=" . $file['id'] . "&action=download";
                                
                                // Status badge class
                                $status_class = '';
                                switch($file['status']) {
                                    case 'approved': $status_class = 'badge-success'; break;
                                    case 'rejected': $status_class = 'badge-danger'; break;
                                    case 'revision': $status_class = 'badge-warning'; break;
                                    default: $status_class = 'badge-secondary';
                                }
                                ?>
                                <div class="col-lg-4 col-md-6 mb-4">
                                    <div class="card shadow h-100">
                                        <div class="card-header py-2">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <small class="text-muted"><?= date('d/m/Y', strtotime($file['created_at'])) ?></small>
                                                <span class="badge <?= $status_class ?> status-badge"><?= ucfirst($file['status']) ?></span>
                                            </div>
                                        </div>
                                        <div class="card-body text-center">
                                            <?php if ($is_image): ?>
                                                <img src="<?= $file_url ?>" alt="Preview" class="file-preview mb-3 img-thumbnail">
                                            <?php else: ?>
                                                <div class="mb-3">
                                                    <?php
                                                    $icon_class = 'fa-file';
                                                    if ($file_ext == 'pdf') $icon_class = 'fa-file-pdf';
                                                    elseif ($file_ext == 'dwg') $icon_class = 'fa-drafting-compass';
                                                    ?>
                                                    <i class="fas <?= $icon_class ?> file-icon"></i>
                                                </div>
                                            <?php endif; ?>
                                            
                                            <h6 class="card-title"><?= htmlspecialchars($file['deskripsi']) ?></h6>
                                            <p class="card-text small text-muted">
                                                <?= htmlspecialchars($file['nama_kegiatan'] ?? 'Tidak terkait tugas') ?><br>
                                                <small>Diupload oleh: <?= htmlspecialchars($file['uploader_name'] ?? 'Unknown') ?></small>
                                            </p>
                                        </div>
                                        <div class="card-footer">
                                            <div class="btn-group w-100" role="group">
                                                <a href="<?= $file_url ?>" target="_blank" class="btn btn-info btn-sm">
                                                    <i class="fas fa-eye"></i> Lihat
                                                </a>
                                                <a href="<?= $download_url ?>" class="btn btn-success btn-sm">
                                                    <i class="fas fa-download"></i> Download
                                                </a>

                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Footer -->
            <footer class="sticky-footer bg-white">
                <div class="container my-auto">
                    <div class="copyright text-center my-auto">
                        <span>Copyright &copy; Client Portal 2025</span>
                    </div>
                </div>
            </footer>
        </div>
    </div>



    <!-- Bootstrap core JavaScript-->
    <script src="../tmp/vendor/jquery/jquery.min.js"></script>
    <script src="../tmp/vendor/bootstrap/js/bootstrap.bundle.min.js"></script>
    <script src="../tmp/js/sb-admin-2.min.js"></script>

    <script>


        function getStatusClass(status) {
            switch(status) {
                case 'approved': return 'success';
                case 'rejected': return 'danger';
                case 'revision': return 'warning';
                default: return 'secondary';
            }
        }

        function getCommentTypeClass(type) {
            switch(type) {
                case 'approval': return 'success';
                case 'rejection': return 'danger';
                case 'revision': return 'warning';
                default: return 'info';
            }
        }

        function formatDate(dateString) {
            const date = new Date(dateString);
            return date.toLocaleDateString('id-ID') + ' ' + date.toLocaleTimeString('id-ID');
        }

        function formatFileSize(bytes) {
            if (!bytes) return 'Unknown';
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(1024));
            return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
        }
    </script>
</body>
</html>
